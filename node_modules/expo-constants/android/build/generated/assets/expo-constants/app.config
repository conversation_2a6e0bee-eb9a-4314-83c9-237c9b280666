{"scheme": "com.dhamma", "name": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>_<PERSON>ram<PERSON>", "description": "React Native Recipes App Demo. Download source code for free at https://www.instamobile.io . You can also check out the Swift version at https://www.iosapptemplates.com and the Kotlin version at https://www.instakotlin.com .", "platforms": ["ios", "android"], "version": "1.0.0", "orientation": "portrait", "newArchEnabled": true, "icon": "./assets/ios/1024x1024.png", "splash": {"resizeMode": "contain", "backgroundColor": "#DDDDDD00"}, "updates": {"fallbackToCacheTimeout": 0}, "plugins": [["@react-native-firebase/app"], ["expo-build-properties", {"android": {"compileSdkVersion": 36, "targetSdkVersion": 36, "minSdkVersion": 31, "buildToolsVersion": "36.0.0", "enableProguardInReleaseBuilds": true, "gradleProperties": {"android.useAndroidX": "true", "android.enableJetifier": "true"}}, "ios": {"useFrameworks": "static", "deploymentTarget": "15.6", "hermesEngineBuildType": "Release"}}], ["expo-splash-screen", {"image": "./assets/ios/1024x1024.png", "imageWidth": 100, "backgroundColor": "#70c1b3", "dark": {"image": "./assets/ios/1024x1024.png", "backgroundColor": "#70c1b3"}, "ios": {"image": "./assets/ios/1024x1024.png", "imageWidth": 100, "backgroundColor": "#70c1b3", "dark": {"image": "./assets/ios/1024x1024.png", "backgroundColor": "#70c1b3"}}}], ["./plugins/injectedGradle.js"], ["./plugins/injectedAndroidConfig.js"], ["./plugins/withDisableForcedDarkModeAndroid.js"], "expo-asset", ["expo-video", {"supportsBackgroundPlayback": true, "supportsPictureInPicture": true}], ["expo-screen-orientation", {"initialOrientation": "PORTRAIT"}]], "assetBundlePatterns": ["**/*"], "ios": {"icon": "./assets/ios/1024x1024.png", "supportsTablet": true, "bundleIdentifier": "com.dhamma", "buildNumber": "1", "infoPlist": {"SKAdNetworkItems": [{"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}], "NSPhotoLibraryUsageDescription": "your description here", "NSSpeechRecognitionUsageDescription": "your description here", "UIRequiresFullScreen": true, "UISupportedInterfaceOrientations": ["UIInterfaceOrientationPortrait", "UIInterfaceOrientationPortraitUpsideDown"], "UIBackgroundModes": ["remote-notification"], "FirebaseAppDelegateProxyEnabled": false}, "privacyManifests": {"NSPrivacyAccessedAPITypes": [{"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryDiskSpace", "NSPrivacyAccessedAPITypeReasons": ["E174.1"]}, {"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategorySystemBootTime", "NSPrivacyAccessedAPITypeReasons": ["8FFB.1"]}, {"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryFileTimestamp", "NSPrivacyAccessedAPITypeReasons": ["DDA9.1"]}, {"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryUserDefaults", "NSPrivacyAccessedAPITypeReasons": ["CA92.1"]}]}, "googleServicesFile": "./GoogleService-Info.plist"}, "android": {"icon": "./assets/ios/1024x1024.png", "package": "com.dhamma", "versionCode": 55, "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION", "android.permission.INTERNET"], "googleServicesFile": "./google-services.json"}, "sdkVersion": "53.0.0", "androidStatusBar": {"backgroundColor": "#70c1b3"}}