/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/android \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=31 \
  -DANDROID_PLATFORM=android-31 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/o6u456u5/obj/arm64-v8a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/o6u456u5/obj/arm64-v8a \
  -DCMAKE_BUILD_TYPE=Debug \
  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/android/.cxx/Debug/o6u456u5/prefab/arm64-v8a/prefab \
  -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/android/.cxx/Debug/o6u456u5/arm64-v8a \
  -GNinja \
  -DANDROID_STL=c++_shared \
  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON \
  -DREACT_NATIVE_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/react-native \
  -DREACT_NATIVE_TARGET_VERSION=79 \
  -DUSE_HERMES=false \
  -DIS_NEW_ARCHITECTURE_ENABLED=false \
  -DUNIT_TEST=false
