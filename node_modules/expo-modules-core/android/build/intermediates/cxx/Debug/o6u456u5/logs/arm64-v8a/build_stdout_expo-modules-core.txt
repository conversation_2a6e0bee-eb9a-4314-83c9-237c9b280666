ninja: Entering directory `/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/android/.cxx/Debug/o6u456u5/arm64-v8a'
[1/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o
[2/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o
[3/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o
[4/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o
[5/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o
[6/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o
[7/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o
[8/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o
[9/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o
[10/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o
[11/41] Building CXX object CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o
[12/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o
[13/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o
[14/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o
[15/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o
[16/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o
[17/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o
[18/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o
[19/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o
[20/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o
[21/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o
[22/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o
[23/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o
[24/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o
[25/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o
[26/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o
[27/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o
[28/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o
[29/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o
[30/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o
[31/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o
[32/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o
[33/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o
[34/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o
[35/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o
[36/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o
[37/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o
[38/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o
[39/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o
[40/41] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o
[41/41] Linking CXX shared library ../../../../build/intermediates/cxx/Debug/o6u456u5/obj/arm64-v8a/libexpo-modules-core.so
