# ninja log v5
3	1502	1760024938257141223	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o	ceb5f6e94442a32b
2	1533	1760024938288214728	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o	7bb4b34d4acc85b0
2	1534	1760024938287437768	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	a35a356f35c7a53e
4	1555	1760024938310398166	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o	6cad1ee3736f96f9
9	1667	1760024938422189610	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	1eeae090b66651a3
7	1845	1760024938598132320	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	1f255c3ed5aefdf5
3	1894	1760024938646461125	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o	4011d8d86d87d587
1	2199	1760024938948877321	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	163008cfe2cc3dcf
10	2389	1760024939140762243	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	d7c5afe9279b4455
1667	2558	1760024939314111404	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o	f9284b7f9900aeb8
1	3049	1760024939802054461	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-ios/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o	3c938a6bb4dd3d41
9	3088	1760024939840563071	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	1515447ddd1eabeb
1894	3148	1760024939901231202	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	b475ac80aa0bfc90
5	3444	1760024940195665125	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	fa63d54691741a2d
1504	3911	1760024940649689877	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	f96efbe0a01e4872
1555	4093	1760024940835933783	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	c35d5bcbcf2d52a8
2390	4746	1760024941490508233	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	4c1f204f26e36625
2199	5132	1760024941883522562	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	94f2eca930a37f12
3149	5214	1760024941965932755	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	e03f757d7275a89b
1534	5376	1760024942101933893	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o	7927b378c7b9a8d7
1533	5451	1760024942201617386	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o	46d994512013b1a4
4093	5669	1760024942423511809	CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o	1a613013b7b66768
3049	5769	1760024942521322255	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	f60a8afa87e24e08
3088	5973	1760024942719939196	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	2d93b09658b6b464
2558	5986	1760024942735949075	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	bd944f04ecdb295b
3444	6187	1760024942939861740	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o	100725a90baf1dd1
5214	6393	1760024943148577919	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	c66805d26af4b97c
5132	7054	1760024943804143621	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	a760a8ab79a496ca
4746	7092	1760024943843691401	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	2abfc65b1acfc1fe
3911	7431	1760024944173107507	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	ddabce42c3c14f65
5451	7660	1760024944410804767	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	fae24816516a40e0
5974	7956	1760024944703213559	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o	8bef52dd5113d325
5769	7971	1760024944721009985	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o	56bf1e76aebaf1f7
5378	8342	1760024945091945293	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	f7f14de004c1a91c
6187	8343	1760024945096675306	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o	bc7bad3754117e2f
5987	8352	1760024945103855702	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o	77916ab706fa15f4
6393	8410	1760024945161532450	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o	9de3bfa6104e3b18
1845	8473	1760024945218117194	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	cc0b77f4d60c4508
5670	8593	1760024945344991723	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	626a92707e46d05b
7055	8809	1760024945562131800	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o	d1b59cf0e72aa5ca
8809	8980	1760024945716784866	../../../../build/intermediates/cxx/Debug/o6u456u5/obj/arm64-v8a/libexpo-modules-core.so	a5f3f1eddb2ff144
