/**
 * Automatically generated by expo-modules-autolinking.
 *
 * This autogenerated class provides a list of classes of native Expo modules,
 * but only these that are written in Swift and use the new API for creating Expo modules.
 */

import ExpoModulesCore
import Expo
import ExpoAsset
import EXConstants
import EASClient
import ExpoFileSystem
import ExpoFont
import ExpoImage
import ExpoKeepAwake
import ExpoLinearGradient
import ExpoLinking
import ExpoScreenOrientation
import ExpoSharing
import ExpoSplashScreen
import EXUpdates
import ExpoVideo

@objc(ExpoModulesProvider)
public class ExpoModulesProvider: ModulesProvider {
  public override func getModuleClasses() -> [AnyModule.Type] {
    return [
      ExpoFetchModule.self,
      AssetModule.self,
      ConstantsModule.self,
      EASClientModule.self,
      FileSystemModule.self,
      FileSystemNextModule.self,
      FontLoaderModule.self,
      FontUtilsModule.self,
      ImageModule.self,
      KeepAwakeModule.self,
      LinearGradientModule.self,
      ExpoLinkingModule.self,
      ScreenOrientationModule.self,
      SharingModule.self,
      SplashScreenModule.self,
      UpdatesModule.self,
      VideoModule.self
    ]
  }

  public override func getAppDelegateSubscribers() -> [ExpoAppDelegateSubscriber.Type] {
    return [
      FileSystemBackgroundSessionHandler.self,
      LinkingAppDelegateSubscriber.self,
      ScreenOrientationAppDelegate.self,
      SplashScreenAppDelegateSubscriber.self
    ]
  }

  public override func getReactDelegateHandlers() -> [ExpoReactDelegateHandlerTupleType] {
    return [
      (packageName: "expo-screen-orientation", handler: ScreenOrientationReactDelegateHandler.self),
      (packageName: "expo-updates", handler: ExpoUpdatesReactDelegateHandler.self)
    ]
  }

  public override func getAppCodeSignEntitlements() -> AppCodeSignEntitlements {
    return AppCodeSignEntitlements.from(json: #"{}"#)
  }
}
