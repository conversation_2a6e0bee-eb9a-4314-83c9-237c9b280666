ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_CXX_LANGUAGE_STANDARD = c++20
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/EASClient" "${PODS_CONFIGURATION_BUILD_DIR}/EXConstants" "${PODS_CONFIGURATION_BUILD_DIR}/EXJSONUtils" "${PODS_CONFIGURATION_BUILD_DIR}/EXManifests" "${PODS_CONFIGURATION_BUILD_DIR}/EXStructuredHeaders" "${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates" "${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface" "${PODS_CONFIGURATION_BUILD_DIR}/Expo" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoScreenOrientation" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSharing" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDatabase" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBDatabase" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/RNFlashList" "${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler" "${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated" "${PODS_CONFIGURATION_BUILD_DIR}/RNSVG" "${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift" "${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "${PODS_CONFIGURATION_BUILD_DIR}/React-hermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling" "${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-oscompat" "${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageAVIFCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageSVGCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftAudioEx" "${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/fmt" "${PODS_CONFIGURATION_BUILD_DIR}/glog" "${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library" "${PODS_CONFIGURATION_BUILD_DIR}/libavif" "${PODS_CONFIGURATION_BUILD_DIR}/libdav1d" "${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-keyboard-controller" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-slider" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-track-player" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-video" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview" "${PODS_ROOT}/FirebaseAnalytics/Frameworks" "${PODS_ROOT}/GoogleAppMeasurement/Frameworks" "${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/Core" "${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/IdentitySupport" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/Core" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/IdentitySupport" "${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) SD_WEBP=1 $(inherited) PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion/DoubleConversion.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EASClient/EASClient.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EXJSONUtils/EXJSONUtils.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EXManifests/EXManifests.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EXStructuredHeaders/EXStructuredHeaders.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates/EXUpdates.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface/EXUpdatesInterface.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Expo/Expo.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset/ExpoAsset.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem/ExpoFileSystem.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont/ExpoFont.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage/ExpoImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake/ExpoKeepAwake.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient/ExpoLinearGradient.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking/ExpoLinking.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/ExpoModulesCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoScreenOrientation/ExpoScreenOrientation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSharing/ExpoSharing.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen/ExpoSplashScreen.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo/ExpoVideo.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop/FirebaseAppCheckInterop.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDatabase/FirebaseDatabase.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift/FirebaseSharedSwift.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/folly.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation/RCTDeprecation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety/RCTTypeSafety.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker/RNCPicker.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics/RNFBAnalytics.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp/RNFBApp.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBDatabase/RNFBDatabase.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging/RNFBMessaging.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNFlashList/RNFlashList.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler/RNGestureHandler.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated/RNReanimated.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVG.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RNScreens/RNScreens.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift/Reachability.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules/CoreModules.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents/React_FabricComponents.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage/React_FabricImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager/React_ImageManager.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer/React_Mapbuffer.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation/RCTAnimation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate/React_RCTAppDelegate.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob/RCTBlob.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/FBReactNativeSpec.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric/RCTFabric.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage/RCTImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking/RCTLinking.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork/RCTNetwork.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime/RCTRuntime.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings/RCTSettings.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText/RCTText.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration/RCTVibration.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple/React_RuntimeApple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore/React_RuntimeCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes/React_RuntimeHermes.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/cxxreact.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule/React_defaultsnativemodule.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule/React_domnativemodule.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule/React_featureflagsnativemodule.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-hermes/reacthermes.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule/idlecallbacksnativemodule.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler/React_jserrorhandler.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsi/jsi.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor/jsireact.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-logger/logger.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule/React_microtasksnativemodule.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-oscompat/oscompat.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger/reactperflogger.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline/React_performancetimeline.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency/React_rendererconsistency.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss/React_renderercss.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler/React_runtimescheduler.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider/ReactAppDependencyProvider.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen/ReactCodegen.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageAVIFCoder/SDWebImageAVIFCoder.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageSVGCoder/SDWebImageSVGCoder.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket/SocketRocket.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftAudioEx/SwiftAudioEx.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Yoga/yoga.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/fmt/fmt.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/glog/glog.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library/leveldb.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/libavif/libavif.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/libdav1d/libdav1d.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values/react_native_get_random_values.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-keyboard-controller/react_native_keyboard_controller.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context/react_native_safe_area_context.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-slider/react_native_slider.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-track-player/react_native_track_player.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-video/react_native_video.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview/react_native_webview.framework/Headers" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/FBLazyVector" "${PODS_ROOT}/Headers/Public/Firebase" "${PODS_ROOT}/Headers/Public/RCTRequired" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-timing" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fast_float" "${PODS_ROOT}/Headers/Public/hermes-engine" "$(PODS_ROOT)/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/EXManifests/Swift Compatibility Header" "${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates/Swift Compatibility Header" "${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface/Swift Compatibility Header" "${PODS_CONFIGURATION_BUILD_DIR}/Expo/Swift Compatibility Header" "$(PODS_ROOT)/Headers/Private/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/Swift Compatibility Header" "$(PODS_ROOT)/Headers/Private/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoScreenOrientation/Swift Compatibility Header" $(inherited) ${PODS_ROOT}/Firebase/CoreOnly/Sources "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost-for-react-native" "$(PODS_ROOT)/glog" "$(PODS_ROOT)/RCT-Folly" "$(PODS_ROOT)/Headers/Public/React-hermes" "$(PODS_ROOT)/Headers/Public/hermes-engine" "$(PODS_ROOT)/../../node_modules/react-native/ReactCommon" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/apple" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/Common/cpp" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/Yoga"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift
OTHER_CFLAGS = $(inherited)  $(inherited) -DRCT_NEW_ARCH_ENABLED  -DREACT_NATIVE_MINOR_VERSION=79 -DREANIMATED_VERSION=3.19.1  $(inherited) -DREACT_NATIVE_MINOR_VERSION=79 -DNDEBUG
OTHER_CPLUSPLUSFLAGS = $(inherited) -DNDEBUG -DRCT_NEW_ARCH_ENABLED=1 -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32
OTHER_LDFLAGS = $(inherited) -ObjC -l"c++" -l"c++abi" -l"icucore" -l"sqlite3" -l"z" -framework "Accelerate" -framework "AudioToolbox" -framework "CFNetwork" -framework "CoreGraphics" -framework "CoreModules" -framework "CoreTelephony" -framework "DoubleConversion" -framework "EASClient" -framework "EXConstants" -framework "EXJSONUtils" -framework "EXManifests" -framework "EXStructuredHeaders" -framework "EXUpdates" -framework "EXUpdatesInterface" -framework "Expo" -framework "ExpoAsset" -framework "ExpoFileSystem" -framework "ExpoFont" -framework "ExpoImage" -framework "ExpoKeepAwake" -framework "ExpoLinearGradient" -framework "ExpoLinking" -framework "ExpoModulesCore" -framework "ExpoScreenOrientation" -framework "ExpoSharing" -framework "ExpoSplashScreen" -framework "ExpoVideo" -framework "FBLPromises" -framework "FBReactNativeSpec" -framework "FirebaseAnalytics" -framework "FirebaseAppCheckInterop" -framework "FirebaseCore" -framework "FirebaseCoreExtension" -framework "FirebaseCoreInternal" -framework "FirebaseDatabase" -framework "FirebaseInstallations" -framework "FirebaseMessaging" -framework "FirebaseSharedSwift" -framework "Foundation" -framework "GoogleAppMeasurement" -framework "GoogleAppMeasurementIdentitySupport" -framework "GoogleDataTransport" -framework "GoogleUtilities" -framework "ImageIO" -framework "JSITooling" -framework "MobileCoreServices" -framework "QuartzCore" -framework "RCTAnimation" -framework "RCTBlob" -framework "RCTDeprecation" -framework "RCTFabric" -framework "RCTImage" -framework "RCTLinking" -framework "RCTNetwork" -framework "RCTRuntime" -framework "RCTSettings" -framework "RCTText" -framework "RCTTypeSafety" -framework "RCTVibration" -framework "RNCAsyncStorage" -framework "RNCPicker" -framework "RNFBAnalytics" -framework "RNFBApp" -framework "RNFBDatabase" -framework "RNFBMessaging" -framework "RNFlashList" -framework "RNGestureHandler" -framework "RNReanimated" -framework "RNSVG" -framework "RNScreens" -framework "Reachability" -framework "React" -framework "ReactAppDependencyProvider" -framework "ReactCodegen" -framework "ReactCommon" -framework "React_Fabric" -framework "React_FabricComponents" -framework "React_FabricImage" -framework "React_ImageManager" -framework "React_Mapbuffer" -framework "React_NativeModulesApple" -framework "React_RCTAppDelegate" -framework "React_RuntimeApple" -framework "React_RuntimeCore" -framework "React_RuntimeHermes" -framework "React_debug" -framework "React_defaultsnativemodule" -framework "React_domnativemodule" -framework "React_featureflags" -framework "React_featureflagsnativemodule" -framework "React_graphics" -framework "React_jserrorhandler" -framework "React_microtasksnativemodule" -framework "React_performancetimeline" -framework "React_rendererconsistency" -framework "React_renderercss" -framework "React_rendererdebug" -framework "React_runtimescheduler" -framework "React_utils" -framework "SDWebImage" -framework "SDWebImageAVIFCoder" -framework "SDWebImageSVGCoder" -framework "SDWebImageWebPCoder" -framework "Security" -framework "SocketRocket" -framework "StoreKit" -framework "SwiftAudioEx" -framework "SystemConfiguration" -framework "UIKit" -framework "cxxreact" -framework "fmt" -framework "folly" -framework "glog" -framework "hermes" -framework "idlecallbacksnativemodule" -framework "jsi" -framework "jsinspector_modern" -framework "jsinspector_moderntracing" -framework "jsireact" -framework "leveldb" -framework "libavif" -framework "libdav1d" -framework "libwebp" -framework "logger" -framework "nanopb" -framework "oscompat" -framework "react_native_get_random_values" -framework "react_native_keyboard_controller" -framework "react_native_safe_area_context" -framework "react_native_slider" -framework "react_native_track_player" -framework "react_native_video" -framework "react_native_webview" -framework "reacthermes" -framework "reactperflogger" -framework "yoga" -weak_framework "JavaScriptCore" -weak_framework "UserNotifications"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "-F${PODS_CONFIGURATION_BUILD_DIR}/EASClient" "-F${PODS_CONFIGURATION_BUILD_DIR}/EXConstants" "-F${PODS_CONFIGURATION_BUILD_DIR}/EXJSONUtils" "-F${PODS_CONFIGURATION_BUILD_DIR}/EXManifests" "-F${PODS_CONFIGURATION_BUILD_DIR}/EXStructuredHeaders" "-F${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates" "-F${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface" "-F${PODS_CONFIGURATION_BUILD_DIR}/Expo" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoScreenOrientation" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoSharing" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBLazyVector" "-F${PODS_CONFIGURATION_BUILD_DIR}/Firebase" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAnalytics" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDatabase" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleAppMeasurement" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "-F${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTRequired" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFBDatabase" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNFlashList" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNSVG" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift" "-F${PODS_CONFIGURATION_BUILD_DIR}/React" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTActionSheet" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-callinvoker" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-hermes" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsitracing" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-oscompat" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rncore" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-runtimeexecutor" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-timing" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageAVIFCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageSVGCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftAudioEx" "-F${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "-F${PODS_CONFIGURATION_BUILD_DIR}/boost" "-F${PODS_CONFIGURATION_BUILD_DIR}/fast_float" "-F${PODS_CONFIGURATION_BUILD_DIR}/fmt" "-F${PODS_CONFIGURATION_BUILD_DIR}/glog" "-F${PODS_CONFIGURATION_BUILD_DIR}/hermes-engine" "-F${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library" "-F${PODS_CONFIGURATION_BUILD_DIR}/libavif" "-F${PODS_CONFIGURATION_BUILD_DIR}/libdav1d" "-F${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "-F${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-keyboard-controller" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-slider" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-track-player" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-video" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
